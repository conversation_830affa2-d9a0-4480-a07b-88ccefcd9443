import express from 'express';
import Database from 'better-sqlite3';

const app = express();

app.use(express.json());

app.get('/', (req, res) => {
  return res.status(200).send({'message': 'SHIPTIVITY API. Read documentation to see API docs'});
});

// We are keeping one connection alive for the rest of the life application for simplicity
const db = new Database('./clients.db');

// Don't forget to close connection when server gets terminated
const closeDb = () => db.close();
process.on('SIGTERM', closeDb);
process.on('SIGINT', closeDb);

/**
 * Validate id input
 * @param {any} id
 */
const validateId = (id) => {
  if (Number.isNaN(id)) {
    return {
      valid: false,
      messageObj: {
      'message': 'Invalid id provided.',
      'long_message': 'Id can only be integer.',
      },
    };
  }
  const client = db.prepare('select * from clients where id = ? limit 1').get(id);
  if (!client) {
    return {
      valid: false,
      messageObj: {
      'message': 'Invalid id provided.',
      'long_message': 'Cannot find client with that id.',
      },
    };
  }
  return {
    valid: true,
  };
}

/**
 * Validate priority input
 * @param {any} priority
 */
const validatePriority = (priority) => {
  if (Number.isNaN(priority)) {
    return {
      valid: false,
      messageObj: {
      'message': 'Invalid priority provided.',
      'long_message': 'Priority can only be positive integer.',
      },
    };
  }
  return {
    valid: true,
  }
}

/**
 * Get all of the clients. Optional filter 'status'
 * GET /api/v1/clients?status={status} - list all clients, optional parameter status: 'backlog' | 'in-progress' | 'complete'
 */
app.get('/api/v1/clients', (req, res) => {
  const status = req.query.status;
  if (status) {
    // status can only be either 'backlog' | 'in-progress' | 'complete'
    if (status !== 'backlog' && status !== 'in-progress' && status !== 'complete') {
      return res.status(400).send({
        'message': 'Invalid status provided.',
        'long_message': 'Status can only be one of the following: [backlog | in-progress | complete].',
      });
    }
    const clients = db.prepare('select * from clients where status = ?').all(status);
    return res.status(200).send(clients);
  }
  const statement = db.prepare('select * from clients');
  const clients = statement.all();
  return res.status(200).send(clients);
});

/**
 * Get a client based on the id provided.
 * GET /api/v1/clients/{client_id} - get client by id
 */
app.get('/api/v1/clients/:id', (req, res) => {
  const id = parseInt(req.params.id , 10);
  const { valid, messageObj } = validateId(id);
  if (!valid) {
    res.status(400).send(messageObj);
  }
  return res.status(200).send(db.prepare('select * from clients where id = ?').get(id));
});

/**
 * Update client information based on the parameters provided.
 * When status is provided, the client status will be changed
 * When priority is provided, the client priority will be changed with the rest of the clients accordingly
 * Note that priority = 1 means it has the highest priority (should be on top of the swimlane).
 * No client on the same status should not have the same priority.
 * This API should return list of clients on success
 *
 * PUT /api/v1/clients/{client_id} - change the status of a client
 *    Data:
 *      status (optional): 'backlog' | 'in-progress' | 'complete',
 *      priority (optional): integer,
 *
 */
app.put('/api/v1/clients/:id', (req, res) => {
  const id = parseInt(req.params.id , 10);
  const { valid, messageObj } = validateId(id);
  if (!valid) {
    res.status(400).send(messageObj);
  }

  let { status, priority } = req.body;
  let clients = db.prepare('select * from clients').all();
  const client = clients.find(client => client.id === id);

  /* ---------- Update code below ----------*/

  // Validate status if provided
  if (status && status !== 'backlog' && status !== 'in-progress' && status !== 'complete') {
    return res.status(400).send({
      'message': 'Invalid status provided.',
      'long_message': 'Status can only be one of the following: [backlog | in-progress | complete].',
    });
  }

  // Validate priority if provided
  if (priority !== undefined) {
    const { valid, messageObj } = validatePriority(priority);
    if (!valid) {
      return res.status(400).send(messageObj);
    }
  }

  const oldStatus = client.status;
  const oldPriority = client.priority;

  // If no changes requested, return current state
  if (!status && priority === undefined) {
    return res.status(200).send(clients);
  }

  // If only status is provided (no priority), add to end of new status column
  if (status && priority === undefined) {
    if (status === oldStatus) {
      // No change needed
      return res.status(200).send(clients);
    }

    // Get max priority for the new status
    const maxPriorityInNewStatus = Math.max(
      0,
      ...clients.filter(c => c.status === status).map(c => c.priority)
    );
    const newPriority = maxPriorityInNewStatus + 1;

    // Update the client
    db.prepare('UPDATE clients SET status = ?, priority = ? WHERE id = ?')
      .run(status, newPriority, id);

    // Reorder priorities in old status (close gaps)
    if (oldStatus !== status) {
      const clientsInOldStatus = clients
        .filter(c => c.status === oldStatus && c.id !== id)
        .sort((a, b) => a.priority - b.priority);

      clientsInOldStatus.forEach((c, index) => {
        db.prepare('UPDATE clients SET priority = ? WHERE id = ?')
          .run(index + 1, c.id);
      });
    }
  }
  // If both status and priority are provided
  else if (status && priority !== undefined) {
    const targetStatus = status;
    const targetPriority = priority;

    // Get all clients in the target status (excluding current client if moving status)
    const clientsInTargetStatus = clients.filter(c =>
      c.status === targetStatus && c.id !== id
    );

    // Validate priority range
    const maxAllowedPriority = clientsInTargetStatus.length + 1;
    if (targetPriority < 1 || targetPriority > maxAllowedPriority) {
      return res.status(400).send({
        'message': 'Invalid priority provided.',
        'long_message': `Priority must be between 1 and ${maxAllowedPriority}.`,
      });
    }

    // Update the client
    db.prepare('UPDATE clients SET status = ?, priority = ? WHERE id = ?')
      .run(targetStatus, targetPriority, id);

    // Reorder other clients in target status
    clientsInTargetStatus
      .filter(c => c.priority >= targetPriority)
      .forEach(c => {
        db.prepare('UPDATE clients SET priority = ? WHERE id = ?')
          .run(c.priority + 1, c.id);
      });

    // Reorder priorities in old status if status changed
    if (oldStatus !== targetStatus) {
      const clientsInOldStatus = clients
        .filter(c => c.status === oldStatus && c.id !== id)
        .sort((a, b) => a.priority - b.priority);

      clientsInOldStatus.forEach((c, index) => {
        db.prepare('UPDATE clients SET priority = ? WHERE id = ?')
          .run(index + 1, c.id);
      });
    }
  }
  // If only priority is provided (same status)
  else if (priority !== undefined && !status) {
    const targetPriority = priority;
    const currentStatus = oldStatus;

    // Get all clients in the current status (excluding current client)
    const clientsInCurrentStatus = clients.filter(c =>
      c.status === currentStatus && c.id !== id
    );

    // Validate priority range
    const maxAllowedPriority = clientsInCurrentStatus.length + 1;
    if (targetPriority < 1 || targetPriority > maxAllowedPriority) {
      return res.status(400).send({
        'message': 'Invalid priority provided.',
        'long_message': `Priority must be between 1 and ${maxAllowedPriority}.`,
      });
    }

    // If priority hasn't changed, no update needed
    if (targetPriority === oldPriority) {
      return res.status(200).send(clients);
    }

    // Update the client priority
    db.prepare('UPDATE clients SET priority = ? WHERE id = ?')
      .run(targetPriority, id);

    // Reorder other clients
    if (targetPriority < oldPriority) {
      // Moving up: shift others down
      clientsInCurrentStatus
        .filter(c => c.priority >= targetPriority && c.priority < oldPriority)
        .forEach(c => {
          db.prepare('UPDATE clients SET priority = ? WHERE id = ?')
            .run(c.priority + 1, c.id);
        });
    } else {
      // Moving down: shift others up
      clientsInCurrentStatus
        .filter(c => c.priority > oldPriority && c.priority <= targetPriority)
        .forEach(c => {
          db.prepare('UPDATE clients SET priority = ? WHERE id = ?')
            .run(c.priority - 1, c.id);
        });
    }
  }

  // Get updated clients list
  const updatedClients = db.prepare('select * from clients').all();

  return res.status(200).send(updatedClients);
});

app.listen(3001);
console.log('app running on port ', 3001);
